<template>
  <div class="add-site-dialog-wrapper">
    <CetDialog
      v-bind="CetDialog_addSite"
      v-on="CetDialog_addSite.event"
      class="add-site-dialog"
    >
      <div class="dialog-content bg-BG1 p-J3">
        <!-- 站点类型选择 -->
        <div class="site-type-section mb-J3">
          <div class="section-title text-T1 font-medium mb-J2">
            {{ $T("选择站点类型") }}
          </div>
          <el-select
            v-model="formData.site_type"
            :placeholder="$T('请选择站点类型')"
            size="medium"
            style="width: 100%"
            @change="handleSiteTypeChange"
          >
            <el-option
              v-for="item in recommendedSiteTypes"
              :key="item.value"
              :label="$T(item.label)"
              :value="item.value"
            />
          </el-select>

          <!-- 资源信息提示 -->
          <div
            v-if="resourceInfo"
            class="resource-info-tip text-T3 text-sm mt-J1"
          >
            {{ $T("当前资源") }}: {{ resourceInfo.name }} ({{
              $T(getResourceTypeName(resourceInfo.type))
            }})
          </div>
        </div>

        <!-- 动态表单区域 -->
        <div v-if="formData.site_type" class="form-section">
          <div class="section-title text-T1 font-medium mb-J2">
            {{ $T("站点信息") }}
          </div>

          <el-form
            ref="siteForm"
            :model="formData"
            :rules="formRules"
            label-width="120px"
            size="medium"
          >
            <!-- 基础信息 - 所有类型都有 -->
            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item :label="$T('站点名称')" prop="site_name">
                  <el-input
                    v-model="formData.site_name"
                    :placeholder="$T('请输入内容')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <!-- 站点类型显示，不可编辑 -->
                <el-form-item :label="$T('站点类型')" prop="site_type_display">
                  <el-input
                    :value="getSiteTypeDisplayName()"
                    :placeholder="$T('请选择')"
                    disabled
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item :label="$T('经度')" prop="longitude">
                  <el-input
                    v-model="formData.longitude"
                    :placeholder="$T('请选择')"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$T('纬度')" prop="latitude">
                  <el-input
                    v-model="formData.latitude"
                    :placeholder="$T('请选择')"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 储能类特有字段 -->
            <template v-if="currentGroup === 'STORAGE'">
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item :label="$T('电压等级')" prop="voltage_level">
                    <el-select
                      v-model="formData.voltage_level"
                      :placeholder="$T('请选择')"
                      style="width: 100%"
                    >
                      <el-option label="0.4kV" value="0.4kV" />
                      <el-option label="10kV" value="10kV" />
                      <el-option label="35kV" value="35kV" />
                      <el-option label="110kV" value="110kV" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$T('并网电压')" prop="grid_voltage">
                    <el-input
                      v-model="formData.grid_voltage"
                      :placeholder="$T('请输入数值')"
                      style="width: 100%"
                    >
                      <template slot="append">kV</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item :label="$T('总装机容量')" prop="total_capacity">
                    <el-input
                      v-model="formData.total_capacity"
                      :placeholder="$T('请输入数值')"
                      style="width: 100%"
                    >
                      <template slot="append">kWh</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$T('总储电量')" prop="total_storage">
                    <el-input
                      v-model="formData.total_storage"
                      :placeholder="$T('请输入数值')"
                      style="width: 100%"
                    >
                      <template slot="append">kWh</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item :label="$T('投运时间')" prop="operation_date">
                    <el-date-picker
                      v-model="formData.operation_date"
                      type="date"
                      :placeholder="$T('请选择日期')"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$T('联系人')" prop="contact_person">
                    <el-input
                      v-model="formData.contact_person"
                      :placeholder="$T('请输入内容')"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item :label="$T('联系电话')" prop="phone_number">
                    <el-input
                      v-model="formData.phone_number"
                      :placeholder="$T('请输入内容')"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$T('关联房间')" prop="related_room">
                    <el-input
                      v-model="selectedRoomName"
                      :placeholder="$T('请选择')"
                      readonly
                      style="width: 100%"
                    >
                      <el-button
                        slot="suffix"
                        type="text"
                        icon="el-icon-paperclip"
                        @click="openRoomSelector"
                        class="room-selector-btn"
                      />
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="24">
                  <el-form-item :label="$T('地址')" prop="site_address">
                    <el-input
                      v-model="formData.site_address"
                      :placeholder="$T('请输入内容')"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <!-- 图片上传 -->
              <el-row>
                <el-col :span="24">
                  <el-form-item :label="$T('图片')" prop="images">
                    <el-upload
                      class="image-uploader"
                      action="#"
                      :show-file-list="false"
                      :before-upload="handleImageUpload"
                      accept="image/png,image/jpg,image/jpeg"
                    >
                      <div v-if="formData.imageUrl" class="image-preview">
                        <img :src="formData.imageUrl" class="uploaded-image" />
                        <div class="image-actions">
                          <i
                            class="el-icon-zoom-in"
                            @click.stop="previewImage"
                          ></i>
                          <i
                            class="el-icon-delete"
                            @click.stop="removeImage"
                          ></i>
                        </div>
                      </div>
                      <div v-else class="upload-placeholder">
                        <i class="el-icon-plus"></i>
                      </div>
                    </el-upload>
                    <div class="upload-tip text-T3 text-sm mt-J1">
                      {{ $T("请上传PNG、JPG、JPEG文件，大小在1M以内") }}
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </template>

            <!-- 新能源类特有字段 -->
            <template v-if="currentGroup === 'RENEWABLE'">
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item :label="$T('电压等级')" prop="voltage_level">
                    <el-select
                      v-model="formData.voltage_level"
                      :placeholder="$T('请选择')"
                      style="width: 100%"
                    >
                      <el-option label="0.4kV" value="0.4kV" />
                      <el-option label="10kV" value="10kV" />
                      <el-option label="35kV" value="35kV" />
                      <el-option label="110kV" value="110kV" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$T('发电模式')" prop="generation_mode">
                    <el-select
                      v-model="formData.generation_mode"
                      :placeholder="$T('请选择')"
                      style="width: 100%"
                    >
                      <el-option label="自发自用" value="self_use" />
                      <el-option label="全额上网" value="full_grid" />
                      <el-option label="余电上网" value="surplus_grid" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item :label="$T('并网电压')" prop="grid_voltage">
                    <el-input
                      v-model="formData.grid_voltage"
                      :placeholder="$T('请输入数值')"
                      style="width: 100%"
                    >
                      <template slot="append">kV</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$T('总装机容量')" prop="total_capacity">
                    <el-input
                      v-model="formData.total_capacity"
                      :placeholder="$T('请输入数值')"
                      style="width: 100%"
                    >
                      <template slot="append">kWh</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item :label="$T('投运时间')" prop="operation_date">
                    <el-date-picker
                      v-model="formData.operation_date"
                      type="date"
                      :placeholder="$T('请选择日期')"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$T('联系人')" prop="contact_person">
                    <el-input
                      v-model="formData.contact_person"
                      :placeholder="$T('请输入内容')"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item :label="$T('联系电话')" prop="phone_number">
                    <el-input
                      v-model="formData.phone_number"
                      :placeholder="$T('请输入内容')"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$T('关联房间')" prop="related_room">
                    <el-input
                      v-model="selectedRoomName"
                      :placeholder="$T('请选择')"
                      readonly
                      style="width: 100%"
                    >
                      <el-button
                        slot="suffix"
                        type="text"
                        icon="el-icon-paperclip"
                        @click="openRoomSelector"
                        class="room-selector-btn"
                      />
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="24">
                  <el-form-item :label="$T('地址')" prop="site_address">
                    <el-input
                      v-model="formData.site_address"
                      :placeholder="$T('请输入内容')"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <!-- 图片上传 -->
              <el-row>
                <el-col :span="24">
                  <el-form-item :label="$T('图片')" prop="images">
                    <el-upload
                      class="image-uploader"
                      action="#"
                      :show-file-list="false"
                      :before-upload="handleImageUpload"
                      accept="image/png,image/jpg,image/jpeg"
                    >
                      <div v-if="formData.imageUrl" class="image-preview">
                        <img :src="formData.imageUrl" class="uploaded-image" />
                        <div class="image-actions">
                          <i
                            class="el-icon-zoom-in"
                            @click.stop="previewImage"
                          ></i>
                          <i
                            class="el-icon-delete"
                            @click.stop="removeImage"
                          ></i>
                        </div>
                      </div>
                      <div v-else class="upload-placeholder">
                        <i class="el-icon-plus"></i>
                      </div>
                    </el-upload>
                    <div class="upload-tip text-T3 text-sm mt-J1">
                      {{ $T("请上传PNG、JPG、JPEG文件，大小在1M以内") }}
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </template>

            <!-- 其他类型特有字段 -->
            <template v-if="currentGroup === 'OTHER'">
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item :label="$T('电压等级')" prop="voltage_level">
                    <el-select
                      v-model="formData.voltage_level"
                      :placeholder="$T('请选择')"
                      style="width: 100%"
                    >
                      <el-option label="0.4kV" value="0.4kV" />
                      <el-option label="10kV" value="10kV" />
                      <el-option label="35kV" value="35kV" />
                      <el-option label="110kV" value="110kV" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$T('投运时间')" prop="operation_date">
                    <el-date-picker
                      v-model="formData.operation_date"
                      type="date"
                      :placeholder="$T('请选择日期')"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item :label="$T('联系人')" prop="contact_person">
                    <el-input
                      v-model="formData.contact_person"
                      :placeholder="$T('请输入内容')"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="$T('联系电话')" prop="phone_number">
                    <el-input
                      v-model="formData.phone_number"
                      :placeholder="$T('请输入内容')"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="24">
                  <el-form-item :label="$T('关联房间')" prop="related_room">
                    <el-input
                      v-model="selectedRoomName"
                      :placeholder="$T('请选择')"
                      readonly
                      style="width: 100%"
                    >
                      <el-button
                        slot="suffix"
                        type="text"
                        icon="el-icon-paperclip"
                        @click="openRoomSelector"
                        class="room-selector-btn"
                      />
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="24">
                  <el-form-item :label="$T('地址')" prop="site_address">
                    <el-input
                      v-model="formData.site_address"
                      :placeholder="$T('请输入内容')"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <!-- 图片上传 -->
              <el-row>
                <el-col :span="24">
                  <el-form-item :label="$T('图片')" prop="images">
                    <el-upload
                      class="image-uploader"
                      action="#"
                      :show-file-list="false"
                      :before-upload="handleImageUpload"
                      accept="image/png,image/jpg,image/jpeg"
                    >
                      <div v-if="formData.imageUrl" class="image-preview">
                        <img :src="formData.imageUrl" class="uploaded-image" />
                        <div class="image-actions">
                          <i
                            class="el-icon-zoom-in"
                            @click.stop="previewImage"
                          ></i>
                          <i
                            class="el-icon-delete"
                            @click.stop="removeImage"
                          ></i>
                        </div>
                      </div>
                      <div v-else class="upload-placeholder">
                        <i class="el-icon-plus"></i>
                      </div>
                    </el-upload>
                    <div class="upload-tip text-T3 text-sm mt-J1">
                      {{ $T("请上传PNG、JPG、JPEG文件，大小在1M以内") }}
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </template>
          </el-form>
        </div>
      </div>

      <!-- 弹窗底部按钮 -->
      <template v-slot:footer>
        <span>
          <el-button @click="handleCancel">
            {{ $T("取消") }}
          </el-button>
          <el-button type="primary" :loading="saving" @click="handleSave">
            {{ $T("确定") }}
          </el-button>
        </span>
      </template>
    </CetDialog>

    <!-- 房间选择弹窗 - 移到外部避免层级问题 -->
    <el-dialog
      :visible.sync="roomSelectorVisible"
      :title="$T('选择关联房间')"
      width="600px"
      :close-on-click-modal="false"
      :modal-append-to-body="false"
      :append-to-body="true"
      class="room-selector-dialog"
    >
      <div class="room-selector-content">
        <!-- 房间类型筛选 -->
        <div class="filter-section mb-J3">
          <el-select
            v-model="selectedRoomType"
            :placeholder="$T('请选择房间类型')"
            @change="handleRoomTypeChange"
            style="width: 200px"
          >
            <el-option
              v-for="type in availableRoomTypes"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </el-select>
        </div>

        <!-- 房间列表 -->
        <div class="room-list" v-loading="roomListLoading">
          <el-table
            :data="filteredRooms"
            @selection-change="handleRoomSelectionChange"
            height="300px"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column
              prop="roomName"
              :label="$T('房间名称')"
              show-overflow-tooltip
              min-width="120"
            />
            <el-table-column
              :label="$T('房间类型')"
              show-overflow-tooltip
              min-width="100"
            >
              <template slot-scope="scope">
                {{ getRoomTypeName(scope.row.roomType) }}
              </template>
            </el-table-column>
            <el-table-column
              prop="roomCode"
              :label="$T('房间编码')"
              show-overflow-tooltip
              min-width="100"
            />
            <el-table-column
              prop="description"
              :label="$T('描述')"
              show-overflow-tooltip
              min-width="120"
            />
          </el-table>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="roomSelectorVisible = false">
          {{ $T("取消") }}
        </el-button>
        <el-button type="primary" @click="confirmRoomSelection">
          {{ $T("确定") }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  SITE_TYPE_OPTIONS,
  getSiteTypeGroup,
  getRecommendedSiteTypeOptions,
  getResourceTypeName
} from "../../../../utils/siteTypes";
import { createSite } from "@/api/site-management";
import { getAvailableRooms } from "@/api/base-config";

export default {
  name: "AddSiteDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    resourceId: {
      type: [String, Number],
      default: null
    },
    resourceInfo: {
      type: Object,
      default: () => null
    },
    vppId: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      // 弹窗配置
      CetDialog_addSite: {
        title: this.$T("新增站点"),
        openTrigger_in: 0,
        closeTrigger_in: 0,
        width: "800px",
        event: {
          openTrigger_out: this.CetDialog_addSite_openTrigger_out,
          closeTrigger_out: this.CetDialog_addSite_closeTrigger_out
        }
      },

      // 站点类型选项
      siteTypeOptions: SITE_TYPE_OPTIONS,

      // 表单数据
      formData: {
        site_type: null,
        site_name: "",
        site_address: "",
        contact_person: "",
        phone_number: "",
        longitude: "",
        latitude: "",
        // 储能类字段
        voltage_level: "",
        grid_voltage: "",
        total_capacity: "",
        total_storage: "",
        operation_date: null,
        related_room: "",
        imageUrl: "",
        // 新能源类字段
        generation_mode: "",
        // 其他类字段
        equipment_scale: "",
        service_scope: ""
      },

      // 表单验证规则
      formRules: {
        site_name: [
          {
            required: true,
            message: this.$T("请输入站点名称"),
            trigger: "blur"
          }
        ],
        site_address: [
          {
            required: true,
            message: this.$T("请输入站点地址"),
            trigger: "blur"
          }
        ],
        contact_person: [
          { required: true, message: this.$T("请输入联系人"), trigger: "blur" }
        ],
        phone_number: [
          {
            required: true,
            message: this.$T("请输入联系电话"),
            trigger: "blur"
          },
          {
            pattern: /^1[3-9]\d{9}$/,
            message: this.$T("请输入正确的手机号码"),
            trigger: "blur"
          }
        ]
      },

      // 保存状态
      saving: false,

      // 房间选择相关
      roomSelectorVisible: false,
      roomListLoading: false,
      selectedRoomType: null,
      selectedRoomId: null,
      selectedRoomName: "",
      allRooms: {},
      selectedRooms: [],
      // 房间类型映射（根据实际API返回的类型编码）
      roomTypeMap: {
        1: "配电室",
        2: "IT机房",
        3: "空调机房",
        4: "空压机房",
        5: "锅炉房",
        6: "管道房",
        7: "蓄电池房",
        8: "储能电站",
        9: "充电站",
        10: "风电场站",
        11: "网络机房",
        12: "光伏电站",
        15: "调光器室",
        16: "发电机室"
      }
    };
  },
  computed: {
    // 当前站点类型分组
    currentGroup() {
      return this.formData.site_type
        ? getSiteTypeGroup(this.formData.site_type)
        : null;
    },

    // 根据资源类型推荐的站点类型选项
    recommendedSiteTypes() {
      if (!this.resourceInfo || !this.resourceInfo.type) {
        return this.siteTypeOptions;
      }

      // 使用新的推荐函数，基于资源类型编码
      const resourceType = this.resourceInfo.type;
      const recommendedOptions = getRecommendedSiteTypeOptions(resourceType);

      // 如果没有匹配的推荐类型，返回所有选项
      return recommendedOptions.length > 0
        ? recommendedOptions
        : this.siteTypeOptions;
    },

    // 动态生成房间类型选项（基于API返回的数据和映射）
    availableRoomTypes() {
      const types = Object.keys(this.allRooms).map(key => ({
        value: key,
        label: this.roomTypeMap[key] || `房间类型${key}`
      }));
      return types;
    },

    // 根据房间类型过滤的房间列表
    filteredRooms() {
      console.log("filteredRooms计算中...");
      console.log("selectedRoomType:", this.selectedRoomType);
      console.log("allRooms:", this.allRooms);

      if (!this.selectedRoomType || !this.allRooms[this.selectedRoomType]) {
        console.log("没有选中房间类型或该类型没有数据");
        return [];
      }

      const rooms = this.allRooms[this.selectedRoomType] || [];
      console.log("过滤后的房间列表:", rooms);
      return rooms;
    }
  },
  watch: {
    visible: {
      handler(newVal) {
        if (newVal) {
          this.CetDialog_addSite.openTrigger_in = Date.now();
        } else {
          this.CetDialog_addSite.closeTrigger_in = Date.now();
        }
      },
      immediate: true
    }
  },
  methods: {
    // 弹窗打开事件
    CetDialog_addSite_openTrigger_out() {
      this.resetForm();
    },

    // 弹窗关闭事件
    CetDialog_addSite_closeTrigger_out() {
      this.$emit("close");
    },

    // 站点类型变化处理
    handleSiteTypeChange() {
      // 清空特有字段
      // 储能类字段
      this.formData.voltage_level = "";
      this.formData.grid_voltage = "";
      this.formData.total_capacity = "";
      this.formData.total_storage = "";
      this.formData.operation_date = null;
      this.formData.related_room = "";
      this.formData.imageUrl = "";
      // 新能源类字段
      this.formData.generation_mode = "";
      // 其他类字段 - 这些字段在OTHER类型中会被使用，所以不需要清空
    },

    // 打开房间选择器
    openRoomSelector() {
      this.roomSelectorVisible = true;
      this.loadAllRooms();
    },

    // 加载所有房间数据
    async loadAllRooms() {
      try {
        this.roomListLoading = true;

        // 调用API获取所有房间数据
        const response = await getAvailableRooms(false);
        console.log("房间API响应:", response);

        if (response.code === 0) {
          // API返回的数据结构是 {[roomType]: [...]}
          this.allRooms = response.data || {};
          console.log("处理后的房间数据:", this.allRooms);

          // 默认选择第一个有数据的房间类型（直接使用API返回的键）
          const availableRoomTypes = Object.keys(this.allRooms).filter(
            key => this.allRooms[key] && this.allRooms[key].length > 0
          );

          if (availableRoomTypes.length > 0) {
            this.selectedRoomType = availableRoomTypes[0];
            console.log("默认选择房间类型:", this.selectedRoomType);
            console.log("可用的房间类型:", availableRoomTypes);
          } else {
            console.log("没有找到有数据的房间类型");
          }
        } else {
          console.error("API返回错误:", response.msg);
          this.$message.error(response.msg || this.$T("获取房间数据失败"));
        }
      } catch (error) {
        console.error("加载房间数据失败:", error);
        this.$message.error(this.$T("加载房间数据失败"));
      } finally {
        this.roomListLoading = false;
      }
    },

    // 房间类型变化处理
    handleRoomTypeChange() {
      this.selectedRooms = [];
    },

    // 房间选择变化处理
    handleRoomSelectionChange(selection) {
      this.selectedRooms = selection;
    },

    // 确认房间选择
    confirmRoomSelection() {
      if (this.selectedRooms.length === 0) {
        this.$message.warning(this.$T("请选择至少一个房间"));
        return;
      }

      // 目前只支持单选，取第一个选中的房间
      const selectedRoom = this.selectedRooms[0];
      this.selectedRoomId = selectedRoom.id;
      this.selectedRoomName = selectedRoom.roomName;
      this.formData.related_room = selectedRoom.roomName;

      this.roomSelectorVisible = false;
    },

    // 根据房间类型编码获取房间类型名称
    getRoomTypeName(roomType) {
      return this.roomTypeMap[roomType] || `房间类型${roomType}`;
    },

    // 取消
    handleCancel() {
      this.$emit("close");
    },

    // 保存
    async handleSave() {
      try {
        // 表单验证
        await this.$refs.siteForm.validate();

        this.saving = true;

        // 生成站点编号（格式：SITE + 时间戳后6位 + 随机2位数字）
        const timestamp = Date.now().toString().slice(-6);
        const random = Math.floor(Math.random() * 100)
          .toString()
          .padStart(2, "0");
        const siteId = `SITE${timestamp}${random}`;

        // 构建保存数据 - 使用API要求的字段名（驼峰命名）
        const saveData = {
          siteId: siteId,
          resourceId: Number(this.resourceId), // 父组件已经处理过格式，直接转数字
          vppId: Number(this.vppId), // 确保是数字类型
          siteType: Number(this.formData.site_type), // 确保是数字类型
          siteName: this.formData.site_name,
          siteAddress: this.formData.site_address,
          contactPerson: this.formData.contact_person,
          phoneNumber: this.formData.phone_number,
          longitude: this.formData.longitude
            ? Number(this.formData.longitude)
            : null, // 确保是数字类型
          latitude: this.formData.latitude
            ? Number(this.formData.latitude)
            : null // 确保是数字类型
        };

        // 根据站点类型添加特有字段（使用驼峰命名，确保数值字段为数字类型）
        if (this.currentGroup === "STORAGE") {
          saveData.voltageLevel = this.formData.voltage_level
            ? Number(this.formData.voltage_level)
            : null;
          saveData.gridVoltage = this.formData.grid_voltage
            ? Number(this.formData.grid_voltage)
            : null;
          saveData.totalCapacity = this.formData.total_capacity
            ? Number(this.formData.total_capacity)
            : null;
          saveData.totalStorage = this.formData.total_storage
            ? Number(this.formData.total_storage)
            : null;
          saveData.operationDate = this.formData.operation_date;
          saveData.roomId = this.selectedRoomId
            ? Number(this.selectedRoomId)
            : null;
          saveData.imageUrl = this.formData.imageUrl;
        } else if (this.currentGroup === "RENEWABLE") {
          saveData.voltageLevel = this.formData.voltage_level
            ? Number(this.formData.voltage_level)
            : null;
          saveData.generationMode = this.formData.generation_mode;
          saveData.gridVoltage = this.formData.grid_voltage
            ? Number(this.formData.grid_voltage)
            : null;
          saveData.totalCapacity = this.formData.total_capacity
            ? Number(this.formData.total_capacity)
            : null;
          saveData.operationDate = this.formData.operation_date;
          saveData.roomId = this.selectedRoomId
            ? Number(this.selectedRoomId)
            : null;
          saveData.imageUrl = this.formData.imageUrl;
        } else if (this.currentGroup === "OTHER") {
          saveData.voltageLevel = this.formData.voltage_level
            ? Number(this.formData.voltage_level)
            : null;
          saveData.operationDate = this.formData.operation_date;
          saveData.roomId = this.selectedRoomId
            ? Number(this.selectedRoomId)
            : null;
          saveData.imageUrl = this.formData.imageUrl;
        }

        // 调用API保存站点数据
        console.log("保存站点数据:", saveData);

        const response = await createSite(saveData);

        if (response.code === 0) {
          this.$message.success(this.$T("站点创建成功"));
          this.$emit("save", response.data);
          this.$emit("close");
        } else {
          throw new Error(response.msg || "创建站点失败");
        }
      } catch (error) {
        console.error("保存站点失败:", error);
        if (error !== false) {
          // 不是表单验证错误
          this.$message.error(this.$T("保存失败，请重试"));
        }
      } finally {
        this.saving = false;
      }
    },

    // 重置表单
    resetForm() {
      this.formData = {
        site_type: null,
        site_name: "",
        site_address: "",
        contact_person: "",
        phone_number: "",
        longitude: "",
        latitude: "",
        // 储能类字段
        voltage_level: "",
        grid_voltage: "",
        total_capacity: "",
        total_storage: "",
        operation_date: null,
        related_room: "",
        imageUrl: "",
        // 新能源类字段
        installed_capacity: null,
        annual_generation: null,
        // 其他类字段
        equipment_scale: "",
        service_scope: ""
      };

      // 重置房间选择相关数据
      this.selectedRoomId = null;
      this.selectedRoomName = "";
      this.selectedRoomType = null;
      this.selectedRooms = [];
      this.roomSelectorVisible = false;

      if (this.$refs.siteForm) {
        this.$refs.siteForm.clearValidate();
      }
    },

    // 获取资源类型标签（使用工具函数）
    getResourceTypeName(resourceType) {
      return getResourceTypeName(resourceType);
    },

    // 获取站点类型显示名称
    getSiteTypeDisplayName() {
      if (!this.formData.site_type) return "";
      const option = this.recommendedSiteTypes.find(
        item => item.value === this.formData.site_type
      );
      return option ? option.label : "";
    },

    // 处理图片上传
    handleImageUpload(file) {
      const isValidType = ["image/png", "image/jpg", "image/jpeg"].includes(
        file.type
      );
      const isValidSize = file.size / 1024 / 1024 < 1;

      if (!isValidType) {
        this.$message.error(this.$T("只能上传PNG、JPG、JPEG格式的图片"));
        return false;
      }
      if (!isValidSize) {
        this.$message.error(this.$T("图片大小不能超过1MB"));
        return false;
      }

      // 创建预览URL
      const reader = new FileReader();
      reader.onload = e => {
        this.formData.imageUrl = e.target.result;
      };
      reader.readAsDataURL(file);

      return false; // 阻止自动上传
    },

    // 预览图片
    previewImage() {
      // TODO: 实现图片预览功能
      console.log("预览图片:", this.formData.imageUrl);
    },

    // 删除图片
    removeImage() {
      this.formData.imageUrl = "";
    }
  }
};
</script>

<style scoped>
/* 包装器样式 - 不影响布局 */
.add-site-dialog-wrapper {
  display: contents;
}

.add-site-dialog .dialog-content {
  max-height: 600px;
  overflow-y: auto;
}

.add-site-dialog .section-title {
  font-size: 16px;
  border-left: 4px solid var(--ZS);
  padding-left: 12px;
}

.add-site-dialog .site-type-section {
  border-bottom: 1px solid var(--BG3);
  padding-bottom: 16px;
}

.add-site-dialog .form-section {
  padding-top: 16px;
}

/* 图片上传样式 */
.image-uploader {
  display: inline-block;
}

.upload-placeholder {
  width: 120px;
  height: 120px;
  border: 2px dashed var(--BG3);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: border-color 0.3s;
}

.upload-placeholder:hover {
  border-color: var(--ZS);
}

.upload-placeholder i {
  font-size: 24px;
  color: var(--T3);
}

.image-preview {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-actions {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  opacity: 0;
  transition: opacity 0.3s;
}

.image-preview:hover .image-actions {
  opacity: 1;
}

.image-actions i {
  color: white;
  font-size: 16px;
  cursor: pointer;
  padding: 4px;
  border-radius: 2px;
  transition: background-color 0.3s;
}

.image-actions i:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.upload-tip {
  margin-top: 8px;
  font-size: 12px;
  color: var(--T3);
}

/* 房间选择器样式 */
.room-selector-btn {
  padding: 0 8px;
  color: var(--ZS);
  font-size: 16px;
}

.room-selector-btn:hover {
  color: var(--ZS-hover);
}

.room-selector-dialog {
  z-index: 3000 !important;
}

.room-selector-dialog .el-dialog__wrapper {
  z-index: 3000 !important;
}

.room-selector-dialog .el-dialog__body {
  padding: 20px;
}

.filter-section {
  margin-bottom: 16px;
}

.room-list {
  border: 1px solid var(--BG3);
  border-radius: 4px;
}
</style>
