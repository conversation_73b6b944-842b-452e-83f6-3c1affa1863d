<template>
  <div class="site-detail-panel">
    <!-- 右侧滑出面板 -->
    <el-drawer
      :visible.sync="visible"
      direction="rtl"
      size="600px"
      :show-close="false"
      :modal="false"
      class="site-detail-drawer"
    >
      <!-- 头部 -->
      <div slot="title" class="drawer-header">
        <div class="header-content">
          <h3 class="title text-T1">{{ $T("详情") }}</h3>
          <el-button
            type="text"
            icon="el-icon-close"
            class="close-btn"
            @click="handleClose"
          />
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="drawer-content" v-if="siteData">
        <!-- 储能类详情信息 -->
        <div v-if="currentGroup === 'STORAGE'" class="info-section">
          <!-- 第一行：站点名称、站点类型、设备数量 -->
          <div class="info-grid info-grid-3">
            <div class="info-item">
              <span class="label text-T2">{{ $T("站点名称") }}</span>
              <span class="value text-T1">{{ siteData.site_name || "-" }}</span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("站点类型") }}</span>
              <span class="value text-T1">
                {{ getSiteTypeName(siteData.site_type) }}
              </span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("设备数量") }}</span>
              <span class="value text-T1">
                {{ siteData.device_count || "10" }}
              </span>
            </div>
          </div>

          <!-- 第二行：经度、纬度、电压等级 -->
          <div class="info-grid info-grid-3">
            <div class="info-item">
              <span class="label text-T2">{{ $T("经度") }}</span>
              <span class="value text-T1">{{ siteData.longitude || "-" }}</span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("纬度") }}</span>
              <span class="value text-T1">{{ siteData.latitude || "-" }}</span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("电压等级") }}</span>
              <span class="value text-T1">
                {{ siteData.voltage_level || "-" }}
              </span>
            </div>
          </div>

          <!-- 第三行：并网电压、总装机容量、总储电量 -->
          <div class="info-grid info-grid-3">
            <div class="info-item">
              <span class="label text-T2">{{ $T("并网电压") }} (kV)</span>
              <span class="value text-T1">
                {{ siteData.grid_voltage || "-" }}
              </span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("总装机容量") }} (kWh)</span>
              <span class="value text-T1">
                {{ siteData.total_capacity || "-" }}
              </span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("总储电量") }} (kWh)</span>
              <span class="value text-T1">
                {{ siteData.total_storage || "-" }}
              </span>
            </div>
          </div>

          <!-- 第四行：投运时间、联系人、联系电话 -->
          <div class="info-grid info-grid-3">
            <div class="info-item">
              <span class="label text-T2">{{ $T("投运时间") }}</span>
              <span class="value text-T1">
                {{ formatDate(siteData.operation_date) }}
              </span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("联系人") }}</span>
              <span class="value text-T1">
                {{ siteData.contact_person || "-" }}
              </span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("联系电话") }}</span>
              <span class="value text-T1">
                {{ siteData.phone_number || "-" }}
              </span>
            </div>
          </div>

          <!-- 第五行：关联房间 -->
          <div class="info-grid info-grid-1">
            <div class="info-item">
              <span class="label text-T2">{{ $T("关联房间") }}</span>
              <span class="value text-T1">
                {{ siteData.related_room || "-" }}
              </span>
            </div>
          </div>

          <!-- 第六行：地址 -->
          <div class="info-grid info-grid-1">
            <div class="info-item">
              <span class="label text-T2">{{ $T("地址") }}</span>
              <span class="value text-T1">
                {{ siteData.site_address || "-" }}
              </span>
            </div>
          </div>

          <!-- 第七行：图片 -->
          <div class="info-grid info-grid-1">
            <div class="info-item">
              <span class="label text-T2">{{ $T("图片") }}</span>
              <div class="image-container">
                <img
                  v-if="siteData.imageUrl"
                  :src="siteData.imageUrl"
                  class="site-image"
                  @click="previewImage"
                  @error="handleImageError"
                />
                <div
                  v-else
                  class="image-placeholder"
                  @click="handleImageUpload"
                >
                  <i class="el-icon-picture-outline"></i>
                  <span class="placeholder-text">{{ $T("暂无图片") }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 新能源类详情信息 -->
        <div v-if="currentGroup === 'RENEWABLE'" class="info-section">
          <!-- 第一行：站点名称、站点类型、设备数量 -->
          <div class="info-grid info-grid-3">
            <div class="info-item">
              <span class="label text-T2">{{ $T("站点名称") }}</span>
              <span class="value text-T1">{{ siteData.site_name || "-" }}</span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("站点类型") }}</span>
              <span class="value text-T1">
                {{ getSiteTypeName(siteData.site_type) }}
              </span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("设备数量") }}</span>
              <span class="value text-T1">
                {{ siteData.device_count || "10" }}
              </span>
            </div>
          </div>

          <!-- 第二行：经度、纬度、电压等级 -->
          <div class="info-grid info-grid-3">
            <div class="info-item">
              <span class="label text-T2">{{ $T("经度") }}</span>
              <span class="value text-T1">{{ siteData.longitude || "-" }}</span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("纬度") }}</span>
              <span class="value text-T1">{{ siteData.latitude || "-" }}</span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("电压等级") }}</span>
              <span class="value text-T1">
                {{ siteData.voltage_level || "-" }}
              </span>
            </div>
          </div>

          <!-- 第三行：发电模式、并网电压、总装机容量 -->
          <div class="info-grid info-grid-3">
            <div class="info-item">
              <span class="label text-T2">{{ $T("发电模式") }}</span>
              <span class="value text-T1">
                {{ getGenerationModeName(siteData.generation_mode) }}
              </span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("并网电压") }} (kV)</span>
              <span class="value text-T1">
                {{ siteData.grid_voltage || "-" }}
              </span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("总装机容量") }} (kWh)</span>
              <span class="value text-T1">
                {{ siteData.total_capacity || "-" }}
              </span>
            </div>
          </div>

          <!-- 第四行：投运时间、联系人、联系电话 -->
          <div class="info-grid info-grid-3">
            <div class="info-item">
              <span class="label text-T2">{{ $T("投运时间") }}</span>
              <span class="value text-T1">
                {{ formatDate(siteData.operation_date) }}
              </span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("联系人") }}</span>
              <span class="value text-T1">
                {{ siteData.contact_person || "-" }}
              </span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("联系电话") }}</span>
              <span class="value text-T1">
                {{ siteData.phone_number || "-" }}
              </span>
            </div>
          </div>

          <!-- 第五行：关联房间 -->
          <div class="info-grid info-grid-1">
            <div class="info-item">
              <span class="label text-T2">{{ $T("关联房间") }}</span>
              <span class="value text-T1">
                {{ siteData.related_room || "-" }}
              </span>
            </div>
          </div>

          <!-- 第六行：地址 -->
          <div class="info-grid info-grid-1">
            <div class="info-item">
              <span class="label text-T2">{{ $T("地址") }}</span>
              <span class="value text-T1">
                {{ siteData.site_address || "-" }}
              </span>
            </div>
          </div>

          <!-- 第七行：图片 -->
          <div class="info-grid info-grid-1">
            <div class="info-item">
              <span class="label text-T2">{{ $T("图片") }}</span>
              <div class="image-container">
                <img
                  v-if="siteData.imageUrl"
                  :src="siteData.imageUrl"
                  class="site-image"
                  @click="previewImage"
                  @error="handleImageError"
                />
                <div
                  v-else
                  class="image-placeholder"
                  @click="handleImageUpload"
                >
                  <i class="el-icon-picture-outline"></i>
                  <span class="placeholder-text">{{ $T("暂无图片") }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 其他类详情信息 -->
        <div v-if="currentGroup === 'OTHER'" class="info-section">
          <!-- 第一行：站点名称、站点类型、设备数量 -->
          <div class="info-grid info-grid-3">
            <div class="info-item">
              <span class="label text-T2">{{ $T("站点名称") }}</span>
              <span class="value text-T1">{{ siteData.site_name || "-" }}</span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("站点类型") }}</span>
              <span class="value text-T1">
                {{ getSiteTypeName(siteData.site_type) }}
              </span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("设备数量") }}</span>
              <span class="value text-T1">
                {{ siteData.device_count || "10" }}
              </span>
            </div>
          </div>

          <!-- 第二行：经度、纬度、电压等级 -->
          <div class="info-grid info-grid-3">
            <div class="info-item">
              <span class="label text-T2">{{ $T("经度") }}</span>
              <span class="value text-T1">{{ siteData.longitude || "-" }}</span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("纬度") }}</span>
              <span class="value text-T1">{{ siteData.latitude || "-" }}</span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("电压等级") }}</span>
              <span class="value text-T1">
                {{ siteData.voltage_level || "-" }}
              </span>
            </div>
          </div>

          <!-- 第三行：投运时间、联系人、联系电话 -->
          <div class="info-grid info-grid-3">
            <div class="info-item">
              <span class="label text-T2">{{ $T("投运时间") }}</span>
              <span class="value text-T1">
                {{ formatDate(siteData.operation_date) }}
              </span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("联系人") }}</span>
              <span class="value text-T1">
                {{ siteData.contact_person || "-" }}
              </span>
            </div>
            <div class="info-item">
              <span class="label text-T2">{{ $T("联系电话") }}</span>
              <span class="value text-T1">
                {{ siteData.phone_number || "-" }}
              </span>
            </div>
          </div>

          <!-- 第四行：关联房间 -->
          <div class="info-grid info-grid-1">
            <div class="info-item">
              <span class="label text-T2">{{ $T("关联房间") }}</span>
              <span class="value text-T1">
                {{ siteData.related_room || "-" }}
              </span>
            </div>
          </div>

          <!-- 第五行：地址 -->
          <div class="info-grid info-grid-1">
            <div class="info-item">
              <span class="label text-T2">{{ $T("地址") }}</span>
              <span class="value text-T1">
                {{ siteData.site_address || "-" }}
              </span>
            </div>
          </div>

          <!-- 第六行：图片 -->
          <div class="info-grid info-grid-1">
            <div class="info-item">
              <span class="label text-T2">{{ $T("图片") }}</span>
              <div class="image-container">
                <img
                  v-if="siteData.imageUrl"
                  :src="siteData.imageUrl"
                  class="site-image"
                  @click="previewImage"
                  @error="handleImageError"
                />
                <div
                  v-else
                  class="image-placeholder"
                  @click="handleImageUpload"
                >
                  <i class="el-icon-picture-outline"></i>
                  <span class="placeholder-text">{{ $T("暂无图片") }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import {
  SITE_TYPE_OPTIONS,
  getSiteTypeGroup
} from "../../../../utils/siteTypes";

export default {
  name: "SiteDetailPanel",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    siteData: {
      type: Object,
      default: null
    }
  },
  computed: {
    // 当前站点类型分组
    currentGroup() {
      return this.siteData && this.siteData.site_type
        ? getSiteTypeGroup(this.siteData.site_type)
        : null;
    }
  },
  methods: {
    // 关闭面板
    handleClose() {
      this.$emit("close");
    },

    // 获取站点类型名称
    getSiteTypeName(siteType) {
      if (!siteType) return "-";
      const option = SITE_TYPE_OPTIONS.find(item => item.value === siteType);
      return option ? option.label : "-";
    },

    // 获取发电模式名称
    getGenerationModeName(mode) {
      const modeMap = {
        self_use: this.$T("自发自用"),
        full_grid: this.$T("全额上网"),
        surplus_grid: this.$T("余电上网")
      };
      return modeMap[mode] || "-";
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return "-";
      if (typeof date === "string") {
        return date.split("T")[0]; // 简单处理ISO日期格式
      }
      if (date instanceof Date) {
        return date.toLocaleDateString();
      }
      return "-";
    },

    // 预览图片
    previewImage() {
      if (!this.siteData.imageUrl) return;

      // 使用Element UI的图片预览功能
      const images = [this.siteData.imageUrl];
      this.$imagePreview(images, 0);
    },

    // 处理图片加载错误
    handleImageError() {
      console.warn("图片加载失败:", this.siteData.imageUrl);
      // 可以设置默认图片或显示错误状态
    },

    // 处理图片上传（占位符点击）
    handleImageUpload() {
      // TODO: 实现图片上传功能
      console.log("点击上传图片");
      this.$message.info(this.$T("图片上传功能待实现"));
    }
  }
};
</script>

<style scoped>
.site-detail-panel {
  width: 100%;
}

.drawer-header {
  padding: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--BG3);
}

.title {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.close-btn {
  font-size: 18px;
  color: var(--T2);
}

.close-btn:hover {
  color: var(--T1);
}

.drawer-content {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.info-section {
  margin-bottom: 24px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
  border-left: 4px solid var(--ZS);
  padding-left: 12px;
}

/* 基础网格 - 两列 */
.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px 24px;
  margin-bottom: 16px;
}

/* 三列网格 */
.info-grid-3 {
  grid-template-columns: 1fr 1fr 1fr;
  gap: 16px 24px;
}

/* 单列网格 */
.info-grid-1 {
  grid-template-columns: 1fr;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.label {
  font-size: 14px;
  font-weight: 500;
  color: var(--T2);
}

.value {
  font-size: 14px;
  word-break: break-all;
  color: var(--T1);
}

.image-container {
  display: flex;
  justify-content: flex-start;
  margin-top: 8px;
}

.site-image {
  max-width: 200px;
  max-height: 200px;
  border-radius: 6px;
  cursor: pointer;
  transition: transform 0.3s;
  border: 1px solid var(--BG3);
}

.site-image:hover {
  transform: scale(1.05);
}

.image-placeholder {
  width: 120px;
  height: 120px;
  border: 2px dashed var(--BG3);
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  background-color: var(--BG2);
}

.image-placeholder:hover {
  border-color: var(--ZS);
  background-color: var(--BG3);
}

.image-placeholder i {
  font-size: 32px;
  color: var(--T3);
  margin-bottom: 8px;
}

.placeholder-text {
  font-size: 12px;
  color: var(--T3);
}

/* 响应式设计 */
@media (max-width: 992px) {
  .info-grid-3 {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 768px) {
  .info-grid,
  .info-grid-3 {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}
</style>
