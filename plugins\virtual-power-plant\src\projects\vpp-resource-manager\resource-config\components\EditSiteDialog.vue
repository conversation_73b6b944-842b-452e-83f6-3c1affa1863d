<template>
  <el-dialog
    :visible.sync="localVisible"
    :title="$T('编辑站点')"
    width="800px"
    :close-on-click-modal="false"
    class="edit-site-dialog"
    @close="handleClose"
  >
    <div class="dialog-content bg-BG1 p-J3">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-text text-T2">{{ $T("加载中...") }}</div>
      </div>

      <!-- 编辑表单 -->
      <div v-else-if="siteData" class="form-section">
        <div class="section-title text-T1 font-medium mb-J2">
          {{ $T("编辑站点信息") }}
        </div>

        <el-form
          ref="editForm"
          :model="formData"
          :rules="formRules"
          label-width="120px"
          size="medium"
        >
          <!-- 站点类型（只读） -->
          <el-form-item :label="$T('站点类型')" prop="site_type">
            <el-input
              v-model="siteTypeName"
              :disabled="true"
              :placeholder="$T('站点类型')"
            />
          </el-form-item>

          <!-- 站点名称 -->
          <el-form-item :label="$T('站点名称')" prop="site_name">
            <el-input
              v-model="formData.site_name"
              :placeholder="$T('请输入站点名称')"
            />
          </el-form-item>

          <!-- 经度 -->
          <el-form-item :label="$T('经度')" prop="longitude">
            <el-input
              v-model="formData.longitude"
              :placeholder="$T('请输入经度')"
              type="number"
            />
          </el-form-item>

          <!-- 纬度 -->
          <el-form-item :label="$T('纬度')" prop="latitude">
            <el-input
              v-model="formData.latitude"
              :placeholder="$T('请输入纬度')"
              type="number"
            />
          </el-form-item>

          <!-- 联系人 -->
          <el-form-item :label="$T('联系人')" prop="contact_person">
            <el-input
              v-model="formData.contact_person"
              :placeholder="$T('请输入联系人')"
            />
          </el-form-item>

          <!-- 联系电话 -->
          <el-form-item :label="$T('联系电话')" prop="phone_number">
            <el-input
              v-model="formData.phone_number"
              :placeholder="$T('请输入联系电话')"
            />
          </el-form-item>

          <!-- 站点地址 -->
          <el-form-item :label="$T('站点地址')" prop="site_address">
            <el-input
              v-model="formData.site_address"
              :placeholder="$T('请输入站点地址')"
              type="textarea"
              :rows="2"
            />
          </el-form-item>

          <!-- 储能类特有字段 -->
          <template v-if="currentGroup === 'STORAGE'">
            <el-row :gutter="20">
              <!-- 电压等级 -->
              <el-col :span="12">
                <el-form-item :label="$T('电压等级')" prop="voltage_level">
                  <el-select
                    v-model="formData.voltage_level"
                    :placeholder="$T('请选择')"
                    style="width: 100%"
                  >
                    <el-option label="0.4kV" value="0.4" />
                    <el-option label="10kV" value="10" />
                    <el-option label="35kV" value="35" />
                    <el-option label="110kV" value="110" />
                    <el-option label="220kV" value="220" />
                  </el-select>
                </el-form-item>
              </el-col>

              <!-- 并网电压 -->
              <el-col :span="12">
                <el-form-item :label="$T('并网电压')" prop="grid_voltage">
                  <el-input
                    v-model="formData.grid_voltage"
                    :placeholder="$T('请输入数值')"
                    type="number"
                  >
                    <template slot="append">kV</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <!-- 总装机容量 -->
              <el-col :span="12">
                <el-form-item :label="$T('总装机容量')" prop="total_capacity">
                  <el-input
                    v-model="formData.total_capacity"
                    :placeholder="$T('请输入数值')"
                    type="number"
                  >
                    <template slot="append">kWh</template>
                  </el-input>
                </el-form-item>
              </el-col>

              <!-- 总储电量 -->
              <el-col :span="12">
                <el-form-item :label="$T('总储电量')" prop="total_storage">
                  <el-input
                    v-model="formData.total_storage"
                    :placeholder="$T('请输入数值')"
                    type="number"
                  >
                    <template slot="append">kWh</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <!-- 投运时间 -->
              <el-col :span="12">
                <el-form-item :label="$T('投运时间')" prop="operation_date">
                  <el-date-picker
                    v-model="formData.operation_date"
                    type="date"
                    :placeholder="$T('请选择日期')"
                    style="width: 100%"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                  />
                </el-form-item>
              </el-col>

              <!-- 联系人 -->
              <el-col :span="12">
                <el-form-item :label="$T('联系人')" prop="contact_person">
                  <el-input
                    v-model="formData.contact_person"
                    :placeholder="$T('请输入内容')"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <!-- 联系电话 -->
              <el-col :span="12">
                <el-form-item :label="$T('联系电话')" prop="phone_number">
                  <el-input
                    v-model="formData.phone_number"
                    :placeholder="$T('请输入内容')"
                  />
                </el-form-item>
              </el-col>

              <!-- 关联房间 -->
              <el-col :span="12">
                <el-form-item :label="$T('关联房间')" prop="related_room">
                  <el-input
                    v-model="formData.related_room"
                    :placeholder="$T('请选择')"
                    readonly
                  >
                    <template slot="append">
                      <el-button
                        icon="el-icon-paperclip"
                        @click="openRoomSelector"
                      />
                    </template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </template>

          <!-- 新能源类特有字段 -->
          <template v-if="currentGroup === 'RENEWABLE'">
            <!-- 发电模式 -->
            <el-form-item :label="$T('发电模式')" prop="generation_mode">
              <el-select
                v-model="formData.generation_mode"
                :placeholder="$T('请选择发电模式')"
                style="width: 100%"
              >
                <el-option :label="$T('自发自用')" value="self_use" />
                <el-option :label="$T('全额上网')" value="full_grid" />
                <el-option :label="$T('余电上网')" value="surplus_grid" />
              </el-select>
            </el-form-item>

            <!-- 电压等级 -->
            <el-form-item :label="$T('电压等级')" prop="voltage_level">
              <el-input
                v-model="formData.voltage_level"
                :placeholder="$T('请输入电压等级')"
              />
            </el-form-item>

            <!-- 并网电压 -->
            <el-form-item :label="$T('并网电压') + ' (kV)'" prop="grid_voltage">
              <el-input
                v-model="formData.grid_voltage"
                :placeholder="$T('请输入并网电压')"
                type="number"
              />
            </el-form-item>

            <!-- 总装机容量 -->
            <el-form-item
              :label="$T('总装机容量') + ' (kWh)'"
              prop="total_capacity"
            >
              <el-input
                v-model="formData.total_capacity"
                :placeholder="$T('请输入总装机容量')"
                type="number"
              />
            </el-form-item>
          </template>

          <!-- 投运时间 -->
          <el-form-item :label="$T('投运时间')" prop="operation_date">
            <el-date-picker
              v-model="formData.operation_date"
              type="date"
              :placeholder="$T('请选择投运时间')"
              style="width: 100%"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 没有数据时显示 -->
      <div v-else class="no-data-container">
        <div class="no-data-text text-T2">{{ $T("没有站点数据") }}</div>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">{{ $T("取消") }}</el-button>
      <el-button type="primary" @click="handleSave">{{ $T("保存") }}</el-button>
    </div>

    <!-- 房间选择弹窗 -->
    <el-dialog
      :title="$T('选择关联房间')"
      :visible.sync="roomSelectorVisible"
      width="800px"
      :modal-append-to-body="false"
      :append-to-body="true"
      class="room-selector-dialog"
    >
      <div class="room-selector-content">
        <!-- 房间类型筛选 -->
        <div class="filter-section mb-J3">
          <el-select
            v-model="selectedRoomType"
            :placeholder="$T('请选择房间类型')"
            @change="handleRoomTypeChange"
            style="width: 200px"
          >
            <el-option
              v-for="type in availableRoomTypes"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </el-select>
        </div>

        <!-- 房间列表 -->
        <div class="room-list" v-loading="roomListLoading">
          <el-table
            :data="filteredRooms"
            @selection-change="handleRoomSelectionChange"
            height="300px"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column
              prop="name"
              :label="$T('房间名称')"
              show-overflow-tooltip
              min-width="120"
            />
            <el-table-column
              :label="$T('房间类型')"
              show-overflow-tooltip
              min-width="100"
            >
              <template slot-scope="scope">
                {{ getRoomTypeName(scope.row.roomtype) }}
              </template>
            </el-table-column>
            <el-table-column
              prop="id"
              :label="$T('房间ID')"
              show-overflow-tooltip
              min-width="80"
            />
          </el-table>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="roomSelectorVisible = false">
          {{ $T("取消") }}
        </el-button>
        <el-button type="primary" @click="confirmRoomSelection">
          {{ $T("确定") }}
        </el-button>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
import {
  SITE_TYPE_OPTIONS,
  getSiteTypeGroup
} from "../../../../utils/siteTypes";
import { getSiteById, updateSite } from "@/api/site-management";
import { getAvailableRooms } from "@/api/room-management";

export default {
  name: "EditSiteDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    siteId: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      loading: false,
      siteData: null,
      formData: {
        site_name: "",
        site_type: null,
        longitude: "",
        latitude: "",
        contact_person: "",
        phone_number: "",
        site_address: "",
        voltage_level: "",
        grid_voltage: "",
        total_capacity: "",
        total_storage: "",
        generation_mode: "",
        operation_date: ""
      },
      formRules: {
        site_name: [
          {
            required: true,
            message: this.$T("请输入站点名称"),
            trigger: "blur"
          }
        ],
        longitude: [
          { required: true, message: this.$T("请输入经度"), trigger: "blur" }
        ],
        latitude: [
          { required: true, message: this.$T("请输入纬度"), trigger: "blur" }
        ]
      },

      // 房间选择相关
      roomSelectorVisible: false,
      roomListLoading: false,
      selectedRoomType: null,
      selectedRoomId: null,
      selectedRoomName: "",
      allRooms: {},
      selectedRooms: [],
      // 房间类型映射（根据实际API返回的类型编码）
      roomTypeMap: {
        1: "配电室",
        2: "IT机房",
        3: "空调机房",
        4: "空压机房",
        5: "锅炉房",
        6: "管道房",
        7: "蓄电池房",
        8: "储能电站",
        9: "充电站",
        10: "风电场站",
        11: "网络机房",
        12: "光伏电站",
        15: "调光器室",
        16: "发电机室"
      }
    };
  },
  computed: {
    // 本地可见状态，避免直接修改prop
    localVisible: {
      get() {
        return this.visible;
      },
      set(value) {
        if (!value) {
          this.handleClose();
        }
      }
    },

    // 当前站点类型分组
    currentGroup() {
      return this.siteData && this.siteData.site_type
        ? getSiteTypeGroup(this.siteData.site_type)
        : null;
    },

    // 站点类型名称
    siteTypeName() {
      if (!this.siteData || !this.siteData.site_type) return "";
      const option = SITE_TYPE_OPTIONS.find(
        item => item.value === this.siteData.site_type
      );
      return option ? this.$T(option.label) : "";
    },

    // 动态生成房间类型选项（基于API返回的数据和映射）
    availableRoomTypes() {
      const types = Object.keys(this.allRooms).map(key => ({
        value: key,
        label: this.roomTypeMap[key] || `房间类型${key}`
      }));
      return types;
    },

    // 根据房间类型过滤的房间列表
    filteredRooms() {
      if (!this.selectedRoomType || !this.allRooms[this.selectedRoomType]) {
        return [];
      }

      return this.allRooms[this.selectedRoomType] || [];
    }
  },
  watch: {
    visible: {
      handler(newVal) {
        if (newVal && this.siteId) {
          this.loadSiteData();
        }
      },
      immediate: true
    }
  },
  methods: {
    // 加载站点数据
    async loadSiteData() {
      if (!this.siteId) return;

      try {
        this.loading = true;
        const response = await getSiteById(this.siteId);

        if (response.code === 0) {
          this.siteData = response.data;
          this.initFormData();
        } else {
          this.$message.error(response.msg || this.$T("获取站点数据失败"));
          this.handleClose();
        }
      } catch (error) {
        console.error("获取站点数据失败:", error);
        this.$message.error(this.$T("获取站点数据失败"));
        this.handleClose();
      } finally {
        this.loading = false;
      }
    },

    // 初始化表单数据
    initFormData() {
      if (!this.siteData) return;

      // API返回的是驼峰命名，直接使用
      this.formData = {
        site_name: this.siteData.siteName || "",
        site_type: this.siteData.siteType,
        longitude: this.siteData.longitude || "",
        latitude: this.siteData.latitude || "",
        contact_person: this.siteData.contactPerson || "",
        phone_number: this.siteData.phoneNumber || "",
        site_address: this.siteData.siteAddress || "",
        // 扩展字段（可能不存在于基础API中）
        voltage_level: this.siteData.voltageLevel || "",
        grid_voltage: this.siteData.gridVoltage || "",
        total_capacity: this.siteData.totalCapacity || "",
        total_storage: this.siteData.totalStorage || "",
        generation_mode: this.siteData.generationMode || "",
        operation_date: this.siteData.operationDate || ""
      };
    },

    // 保存
    async handleSave() {
      try {
        await this.$refs.editForm.validate();

        const updateData = {
          siteName: this.formData.site_name,
          longitude: this.formData.longitude
            ? Number(this.formData.longitude)
            : null,
          latitude: this.formData.latitude
            ? Number(this.formData.latitude)
            : null,
          contactPerson: this.formData.contact_person,
          phoneNumber: this.formData.phone_number,
          siteAddress: this.formData.site_address,
          // 扩展字段（根据站点类型可能需要）
          voltageLevel: this.formData.voltage_level || null,
          gridVoltage: this.formData.grid_voltage
            ? Number(this.formData.grid_voltage)
            : null,
          totalCapacity: this.formData.total_capacity
            ? Number(this.formData.total_capacity)
            : null,
          totalStorage: this.formData.total_storage
            ? Number(this.formData.total_storage)
            : null,
          generationMode: this.formData.generation_mode || null,
          operationDate: this.formData.operation_date || null
        };

        const response = await updateSite(this.siteId, updateData);

        if (response.code === 0) {
          this.$message.success(this.$T("更新站点成功"));
          this.$emit("save", response.data);
          this.handleClose();
        } else {
          this.$message.error(response.msg || this.$T("更新站点失败"));
        }
      } catch (error) {
        if (error.message) {
          // 表单验证错误
          return;
        }
        console.error("更新站点失败:", error);
        this.$message.error(this.$T("更新站点失败"));
      }
    },

    // 关闭
    handleClose() {
      this.$emit("close");
      this.resetForm();
    },

    // 重置表单
    resetForm() {
      this.siteData = null;
      this.formData = {
        site_name: "",
        site_type: null,
        longitude: "",
        latitude: "",
        contact_person: "",
        phone_number: "",
        site_address: "",
        voltage_level: "",
        grid_voltage: "",
        total_capacity: "",
        total_storage: "",
        generation_mode: "",
        operation_date: ""
      };
      if (this.$refs.editForm) {
        this.$refs.editForm.clearValidate();
      }
    },

    // 打开房间选择器
    openRoomSelector() {
      this.roomSelectorVisible = true;
      this.loadAllRooms();
    },

    // 加载所有房间数据
    async loadAllRooms() {
      try {
        this.roomListLoading = true;

        // 调用API获取所有房间数据
        const response = await getAvailableRooms(false);

        if (response.code === 0) {
          // API返回的数据结构是 {[roomType]: [...]}
          this.allRooms = response.data || {};

          // 默认选择第一个有数据的房间类型（直接使用API返回的键）
          const availableRoomTypes = Object.keys(this.allRooms).filter(
            key => this.allRooms[key] && this.allRooms[key].length > 0
          );

          if (availableRoomTypes.length > 0) {
            this.selectedRoomType = availableRoomTypes[0];
          }
        } else {
          this.$message.error(response.msg || this.$T("获取房间数据失败"));
        }
      } catch (error) {
        console.error("加载房间数据失败:", error);
        this.$message.error(this.$T("加载房间数据失败"));
      } finally {
        this.roomListLoading = false;
      }
    },

    // 房间类型变化处理
    handleRoomTypeChange() {
      this.selectedRooms = [];
    },

    // 房间选择变化处理
    handleRoomSelectionChange(selection) {
      this.selectedRooms = selection;
    },

    // 确认房间选择
    confirmRoomSelection() {
      if (this.selectedRooms.length === 0) {
        this.$message.warning(this.$T("请选择至少一个房间"));
        return;
      }

      // 目前只支持单选，取第一个选中的房间
      const selectedRoom = this.selectedRooms[0];
      this.selectedRoomId = selectedRoom.id;
      this.selectedRoomName = selectedRoom.name;
      this.formData.related_room = selectedRoom.name;

      this.roomSelectorVisible = false;
    },

    // 根据房间类型编码获取房间类型名称
    getRoomTypeName(roomType) {
      return this.roomTypeMap[roomType] || `房间类型${roomType}`;
    }
  }
};
</script>

<style scoped>
.edit-site-dialog .dialog-content {
  max-height: 70vh;
  overflow-y: auto;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.loading-text {
  font-size: 14px;
}

.section-title {
  font-size: 16px;
  border-left: 4px solid var(--ZS);
  padding-left: 12px;
}

.no-data-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.no-data-text {
  font-size: 14px;
}
</style>
