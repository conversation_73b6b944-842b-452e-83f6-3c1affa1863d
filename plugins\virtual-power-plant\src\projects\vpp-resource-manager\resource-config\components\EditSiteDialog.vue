<template>
  <el-dialog
    :visible.sync="localVisible"
    :title="$T('编辑站点')"
    width="800px"
    :close-on-click-modal="false"
    class="edit-site-dialog"
    @close="handleClose"
  >
    <div class="dialog-content bg-BG1 p-J3">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-text text-T2">{{ $T("加载中...") }}</div>
      </div>

      <!-- 编辑表单 -->
      <div v-else-if="siteData" class="form-section">
        <div class="section-title text-T1 font-medium mb-J2">
          {{ $T("编辑站点信息") }}
        </div>

        <el-form
          ref="editForm"
          :model="formData"
          :rules="formRules"
          label-width="120px"
          size="medium"
        >
          <!-- 站点类型（只读） -->
          <el-form-item :label="$T('站点类型')" prop="site_type">
            <el-input
              v-model="siteTypeName"
              :disabled="true"
              :placeholder="$T('站点类型')"
            />
          </el-form-item>

          <!-- 站点名称 -->
          <el-form-item :label="$T('站点名称')" prop="site_name">
            <el-input
              v-model="formData.site_name"
              :placeholder="$T('请输入站点名称')"
            />
          </el-form-item>

          <!-- 经度 -->
          <el-form-item :label="$T('经度')" prop="longitude">
            <el-input
              v-model="formData.longitude"
              :placeholder="$T('请输入经度')"
              type="number"
            />
          </el-form-item>

          <!-- 纬度 -->
          <el-form-item :label="$T('纬度')" prop="latitude">
            <el-input
              v-model="formData.latitude"
              :placeholder="$T('请输入纬度')"
              type="number"
            />
          </el-form-item>

          <!-- 联系人 -->
          <el-form-item :label="$T('联系人')" prop="contact_person">
            <el-input
              v-model="formData.contact_person"
              :placeholder="$T('请输入联系人')"
            />
          </el-form-item>

          <!-- 联系电话 -->
          <el-form-item :label="$T('联系电话')" prop="phone_number">
            <el-input
              v-model="formData.phone_number"
              :placeholder="$T('请输入联系电话')"
            />
          </el-form-item>

          <!-- 站点地址 -->
          <el-form-item :label="$T('站点地址')" prop="site_address">
            <el-input
              v-model="formData.site_address"
              :placeholder="$T('请输入站点地址')"
              type="textarea"
              :rows="2"
            />
          </el-form-item>

          <!-- 储能类特有字段 -->
          <template v-if="currentGroup === 'STORAGE'">
            <!-- 电压等级 -->
            <el-form-item :label="$T('电压等级')" prop="voltage_level">
              <el-input
                v-model="formData.voltage_level"
                :placeholder="$T('请输入电压等级')"
              />
            </el-form-item>

            <!-- 并网电压 -->
            <el-form-item :label="$T('并网电压') + ' (kV)'" prop="grid_voltage">
              <el-input
                v-model="formData.grid_voltage"
                :placeholder="$T('请输入并网电压')"
                type="number"
              />
            </el-form-item>

            <!-- 总装机容量 -->
            <el-form-item
              :label="$T('总装机容量') + ' (kWh)'"
              prop="total_capacity"
            >
              <el-input
                v-model="formData.total_capacity"
                :placeholder="$T('请输入总装机容量')"
                type="number"
              />
            </el-form-item>

            <!-- 总储电量 -->
            <el-form-item
              :label="$T('总储电量') + ' (kWh)'"
              prop="total_storage"
            >
              <el-input
                v-model="formData.total_storage"
                :placeholder="$T('请输入总储电量')"
                type="number"
              />
            </el-form-item>
          </template>

          <!-- 新能源类特有字段 -->
          <template v-if="currentGroup === 'RENEWABLE'">
            <!-- 发电模式 -->
            <el-form-item :label="$T('发电模式')" prop="generation_mode">
              <el-select
                v-model="formData.generation_mode"
                :placeholder="$T('请选择发电模式')"
                style="width: 100%"
              >
                <el-option :label="$T('自发自用')" value="self_use" />
                <el-option :label="$T('全额上网')" value="full_grid" />
                <el-option :label="$T('余电上网')" value="surplus_grid" />
              </el-select>
            </el-form-item>

            <!-- 电压等级 -->
            <el-form-item :label="$T('电压等级')" prop="voltage_level">
              <el-input
                v-model="formData.voltage_level"
                :placeholder="$T('请输入电压等级')"
              />
            </el-form-item>

            <!-- 并网电压 -->
            <el-form-item :label="$T('并网电压') + ' (kV)'" prop="grid_voltage">
              <el-input
                v-model="formData.grid_voltage"
                :placeholder="$T('请输入并网电压')"
                type="number"
              />
            </el-form-item>

            <!-- 总装机容量 -->
            <el-form-item
              :label="$T('总装机容量') + ' (kWh)'"
              prop="total_capacity"
            >
              <el-input
                v-model="formData.total_capacity"
                :placeholder="$T('请输入总装机容量')"
                type="number"
              />
            </el-form-item>
          </template>

          <!-- 投运时间 -->
          <el-form-item :label="$T('投运时间')" prop="operation_date">
            <el-date-picker
              v-model="formData.operation_date"
              type="date"
              :placeholder="$T('请选择投运时间')"
              style="width: 100%"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 没有数据时显示 -->
      <div v-else class="no-data-container">
        <div class="no-data-text text-T2">{{ $T("没有站点数据") }}</div>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">{{ $T("取消") }}</el-button>
      <el-button type="primary" @click="handleSave">{{ $T("保存") }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {
  SITE_TYPE_OPTIONS,
  getSiteTypeGroup
} from "../../../../utils/siteTypes";
import { getSiteById, updateSite } from "@/api/site-management";

export default {
  name: "EditSiteDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    siteId: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      loading: false,
      siteData: null,
      formData: {
        site_name: "",
        site_type: null,
        longitude: "",
        latitude: "",
        contact_person: "",
        phone_number: "",
        site_address: "",
        voltage_level: "",
        grid_voltage: "",
        total_capacity: "",
        total_storage: "",
        generation_mode: "",
        operation_date: ""
      },
      formRules: {
        site_name: [
          {
            required: true,
            message: this.$T("请输入站点名称"),
            trigger: "blur"
          }
        ],
        longitude: [
          { required: true, message: this.$T("请输入经度"), trigger: "blur" }
        ],
        latitude: [
          { required: true, message: this.$T("请输入纬度"), trigger: "blur" }
        ]
      }
    };
  },
  computed: {
    // 本地可见状态，避免直接修改prop
    localVisible: {
      get() {
        return this.visible;
      },
      set(value) {
        if (!value) {
          this.handleClose();
        }
      }
    },

    // 当前站点类型分组
    currentGroup() {
      return this.siteData && this.siteData.site_type
        ? getSiteTypeGroup(this.siteData.site_type)
        : null;
    },

    // 站点类型名称
    siteTypeName() {
      if (!this.siteData || !this.siteData.site_type) return "";
      const option = SITE_TYPE_OPTIONS.find(
        item => item.value === this.siteData.site_type
      );
      return option ? this.$T(option.label) : "";
    }
  },
  watch: {
    visible: {
      handler(newVal) {
        if (newVal && this.siteId) {
          this.loadSiteData();
        }
      },
      immediate: true
    }
  },
  methods: {
    // 加载站点数据
    async loadSiteData() {
      console.log("loadSiteData called with siteId:", this.siteId);
      if (!this.siteId) {
        console.log("No siteId, returning");
        return;
      }

      try {
        console.log("Starting to load site data...");
        this.loading = true;
        const response = await getSiteById(this.siteId);
        console.log("API response:", response);

        if (response.code === 0) {
          this.siteData = response.data;
          this.initFormData();
        } else {
          this.$message.error(response.msg || this.$T("获取站点数据失败"));
          this.handleClose();
        }
      } catch (error) {
        console.error("获取站点数据失败:", error);
        this.$message.error(this.$T("获取站点数据失败"));
        this.handleClose();
      } finally {
        this.loading = false;
      }
    },

    // 初始化表单数据
    initFormData() {
      if (!this.siteData) return;

      this.formData = {
        site_name: this.siteData.siteName || this.siteData.site_name || "",
        site_type: this.siteData.siteType || this.siteData.site_type,
        longitude: this.siteData.longitude || "",
        latitude: this.siteData.latitude || "",
        contact_person:
          this.siteData.contactPerson || this.siteData.contact_person || "",
        phone_number:
          this.siteData.phoneNumber || this.siteData.phone_number || "",
        site_address:
          this.siteData.siteAddress || this.siteData.site_address || "",
        voltage_level:
          this.siteData.voltageLevel || this.siteData.voltage_level || "",
        grid_voltage:
          this.siteData.gridVoltage || this.siteData.grid_voltage || "",
        total_capacity:
          this.siteData.totalCapacity || this.siteData.total_capacity || "",
        total_storage:
          this.siteData.totalStorage || this.siteData.total_storage || "",
        generation_mode:
          this.siteData.generationMode || this.siteData.generation_mode || "",
        operation_date:
          this.siteData.operationDate || this.siteData.operation_date || ""
      };
    },

    // 保存
    async handleSave() {
      try {
        await this.$refs.editForm.validate();

        const updateData = {
          siteName: this.formData.site_name,
          longitude: this.formData.longitude,
          latitude: this.formData.latitude,
          contactPerson: this.formData.contact_person,
          phoneNumber: this.formData.phone_number,
          siteAddress: this.formData.site_address,
          voltageLevel: this.formData.voltage_level,
          gridVoltage: this.formData.grid_voltage,
          totalCapacity: this.formData.total_capacity,
          totalStorage: this.formData.total_storage,
          generationMode: this.formData.generation_mode,
          operationDate: this.formData.operation_date
        };

        const response = await updateSite(this.siteId, updateData);

        if (response.code === 0) {
          this.$message.success(this.$T("更新站点成功"));
          this.$emit("save", response.data);
          this.handleClose();
        } else {
          this.$message.error(response.msg || this.$T("更新站点失败"));
        }
      } catch (error) {
        if (error.message) {
          // 表单验证错误
          return;
        }
        console.error("更新站点失败:", error);
        this.$message.error(this.$T("更新站点失败"));
      }
    },

    // 关闭
    handleClose() {
      this.$emit("close");
      this.resetForm();
    },

    // 重置表单
    resetForm() {
      this.siteData = null;
      this.formData = {
        site_name: "",
        site_type: null,
        longitude: "",
        latitude: "",
        contact_person: "",
        phone_number: "",
        site_address: "",
        voltage_level: "",
        grid_voltage: "",
        total_capacity: "",
        total_storage: "",
        generation_mode: "",
        operation_date: ""
      };
      if (this.$refs.editForm) {
        this.$refs.editForm.clearValidate();
      }
    }
  }
};
</script>

<style scoped>
.edit-site-dialog .dialog-content {
  max-height: 70vh;
  overflow-y: auto;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.loading-text {
  font-size: 14px;
}

.section-title {
  font-size: 16px;
  border-left: 4px solid var(--ZS);
  padding-left: 12px;
}

.no-data-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.no-data-text {
  font-size: 14px;
}
</style>
